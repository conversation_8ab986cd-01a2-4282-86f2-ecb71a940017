# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_debit_note
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <davor.bojk<PERSON>@storm.hr>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "<span class=\"o_stat_text\">Debit Notes</span>"
msgstr ""

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr ""

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Cancel"
msgstr "Otkaži"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_journal__debit_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" debit notes made from this journal"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__copy_lines
msgid "Copy Lines"
msgstr "Kopiraj stavke"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__country_code
msgid "Country Code"
msgstr "Šifra države"

#. module: account_debit_note
#: model:ir.actions.act_window,name:account_debit_note.action_view_account_move_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Create Debit Note"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_debit_note
#: model:ir.actions.server,name:account_debit_note.action_move_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_filter_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_line_filter_debit
msgid "Debit Note"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__date
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Debit Note Date"
msgstr ""

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/models/account_move.py:0
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_ids
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_invoice_filter_debit
msgid "Debit Notes"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_journal__debit_sequence
msgid "Dedicated Debit Note Sequence"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__id
msgid "ID"
msgstr "ID"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__journal_id
msgid "If empty, uses the journal of the journal entry to be debited."
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__copy_lines
msgid ""
"In case you need to do corrections for every line, it can be in handy to "
"copy them.  We won't copy them for debit notes from credit notes. "
msgstr ""

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_journal
msgid "Journal"
msgstr "Dnevnik"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_move
msgid "Journal Entry"
msgstr "Temeljnica"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_type
msgid "Journal Type"
msgstr "Tip dnevnika"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_date
msgid "Last Updated on"
msgstr "Promijenjeno"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_ids
msgid "Move"
msgstr "Temeljnica"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_type
msgid "Move Type"
msgstr "Tip temeljnice"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_count
msgid "Number of Debit Notes"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_origin_id
msgid "Original Invoice Debited"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__reason
msgid "Reason"
msgstr "Razlog"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO oznaka države u dva slova.\n"
"Možete koristiti za brzo pretraživanje."

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_move__debit_note_ids
msgid "The debit notes created for this invoice"
msgstr ""

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "This debit note was created from: %s"
msgstr ""

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_id
msgid "Use Specific Journal"
msgstr "Koristi specifični dnevnik"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can make a debit note only for a Customer Invoice, a Customer Credit "
"Note, a Vendor Bill or a Vendor Credit Note."
msgstr ""

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "You can only debit posted moves."
msgstr ""

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can't make a debit note for an invoice that is already linked to a debit"
" note."
msgstr ""
