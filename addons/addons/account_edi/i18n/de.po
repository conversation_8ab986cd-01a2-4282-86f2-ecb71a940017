# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing error(s)"
msgstr "%(count)s Fehler bei der elektronischen Rechnungsstellung"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing info(s)"
msgstr "%(count)s Info(s) zur elektronischen Rechnungsstellung"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing warning(s)"
msgstr "%(count)s Warnung(en) bei der elektronischen Rechnungsstellung"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A cancellation of the EDI has been requested."
msgstr "Ein Antrag auf Löschung des EDI wurde gestellt."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A request for cancellation of the EDI has been called off."
msgstr "Ein Antrag auf Löschung des EDI wurde zurückgenommen."

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_send
msgid "Account Move Send"
msgstr "Kontobuchung senden"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"Are you sure you want to cancel this invoice without waiting for the EDI "
"document to be canceled?"
msgstr ""
"Sind Sie sicher, dass Sie diese Rechnung ohne Erhalt des EDI-Dokuments "
"stornieren möchten?"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Dateianhang"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "Blockierlevel"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"Blockiert den aktuellen Vorgang des Dokuments in Abhängigkeit von der Fehlerschwere:\n"
"* Info: Das Dokument ist nicht blockiert und alles funktioniert wie gewünscht.\n"
"* Warnung: Es liegt ein Fehler vor, der den aktuellen Vorgang der elektronischen Rechnungsstellung nicht behindert.\n"
"* Fehler: Es liegt ein Fehler vor, der den aktuellen Vorgang der elektronischen Rechnungsstellung blockiert."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "EDI-Storno abbrechen"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"Deaktivierung von (%s) ist für dieses Journal nicht möglich, da nicht alle "
"Dokumente synchronisiert sind"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Code"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Kompatible EDI"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Download"
msgstr "Herunterladen"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "EDI Documents"
msgstr "EDI-Dokumente"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "EDI-Format"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "EDI-Format, das Buchungen in diesem Journal unterstützt"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
msgid "EDI: Perform web services operations"
msgstr "EDI: Durchführung von Webservice-Vorgängen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "EDI-Blockierlevel"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "EDI-Inhalt"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
msgid "Edi Document"
msgstr "EDI-Dokument"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
msgid "Edi Error Count"
msgstr "Anzahl EDI-Fehler"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
msgid "Edi Error Message"
msgstr "Edi-Fehlermeldung"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "EDi-Format"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "EDI-Schaltfläche zum Abbrechen der Stornierung anzeigen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "EDI-Schaltfläche zum Stornieren anzeigen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_force_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_force_cancel_button
msgid "Edi Show Force Cancel Button"
msgstr "EDI-Schaltfläche „Stornierung erzwingen“ anzeigen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "Zu verarbeitende Edi-Web-Services"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "Elektronisches Dokument für einen account.move"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
msgid "Electronic invoicing"
msgstr "Elektronische Rechnungsstellung"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "Verarbeitung elektronischer Rechnungen erforderlich"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "Status elektronische Rechnungsstellung"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "Fehler"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Force Cancel"
msgstr "Stornierung erzwingen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "Formatname"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
msgid "How many EDIs are in error for this move?"
msgstr "Wie viele EDIs sind bei diesem Vorgang fehlerhaft?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "ID"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "Information"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"Ungültige Rechnungskonfiguration:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Buchung"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Name"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "Nur ein EDI-Dokument pro Buchung pro Format"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Process now"
msgstr "Jetzt verarbeiten"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Ändern Sie die Reihenfolge der Journalbuchungen."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Berichtsaktion"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "EDI-Storno anfordern"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Retry"
msgstr "Wiederholen"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "XML/EDI-Rechnungen versenden"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Gesendet"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "Status"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr "Der aggregierte Status aller EDIs mit Web-Service dieser Buchung"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"Die Datei, die von edi_format_id erzeugt wird, wenn die Rechnung gebucht "
"wird (und dieses Dokument verarbeitet wird)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""
"Folgende Dokumente wurden bereits gesendet und können nicht resequenziert "
"werden: %s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "The invoice will soon be sent to"
msgstr "Die Rechnung wird bald gesendet an"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"Der Text des letzten Fehlers, der während des Vorgangs der elektronischen "
"Rechnung aufgetreten ist."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "Dieser Code existiert bereits"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
msgid "This document is being sent by another process already. "
msgstr "Dieses Dokument wird bereits von einem anderen Prozess gesendet. "

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"This invoice was canceled while the EDIs %s still had a pending cancellation"
" request."
msgstr ""
"Diese Rechnung wurde storniert, während die EDIs %s immer noch ausstehende "
"Stornierungsanfragen hatten."

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "Abzubrechen"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "Zu senden"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "Warnung"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"Sie können die folgende Journalbuchung %s nicht bearbeiten, da bereits ein "
"elektronisches Dokument versendet wurde. Bitte verwenden Sie stattdessen die"
" Schaltfläche „EDI-Storno anfordern“."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"Sie können die Verknüpfung eines EDI-Dokuments, das an die Regierung "
"gesendet wurde, nicht aufheben."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "⇒ See errors"
msgstr "⇒ Fehler anzeigen"
